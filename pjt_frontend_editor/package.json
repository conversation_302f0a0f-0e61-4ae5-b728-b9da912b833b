{"name": "vue3-vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "test": "cross-env NODE_ENV=test vite --mode test", "build": "vite build", "build-copy": "dev-build-copy-to.bat", "preview": "vite preview"}, "devDependencies": {"@types/node": "^20.5.7", "@vitejs/plugin-vue": "^5.2.1", "@rollup/plugin-babel": "^6.0.3", "babel-plugin-component": "^1.1.1", "less": "^4.2.0", "less-loader": "^11.1.3", "vite": "^6.2.6"}, "dependencies": {"cross-env": "^7.0.3", "element-plus": "^2.9.5", "@element-plus/icons-vue": "^2.3.1", "pinia": "^2.1.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}}