// Dashboard 通用样式

// 颜色变量
@primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@accent-gradient: linear-gradient(135deg, #ff6b6b, #ffa500);
@bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

@primary-color: #667eea;
@accent-color: #ff6b6b;
@text-primary: #1e293b;
@text-secondary: #64748b;
@text-muted: #94a3b8;

@border-color: rgba(226, 232, 240, 0.8);
@bg-white: white;
@bg-light: #f8fafc;
@bg-hover: #f1f5f9;

// 阴影
@shadow-sm: 0 4px 20px rgba(0, 0, 0, 0.08);
@shadow-md: 0 12px 40px rgba(0, 0, 0, 0.15);
@shadow-lg: 0 20px 40px rgba(102, 126, 234, 0.3);

// 圆角
@radius-sm: 8px;
@radius-md: 12px;
@radius-lg: 16px;
@radius-xl: 20px;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 12px;
@spacing-lg: 16px;
@spacing-xl: 20px;
@spacing-2xl: 24px;
@spacing-3xl: 32px;

// 通用按钮样式
.action-btn {
  display: flex;
  align-items: center;
  gap: @spacing-sm;
  padding: @spacing-md @spacing-2xl;
  border: none;
  border-radius: @radius-md;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  .btn-icon {
    font-size: 16px;
  }

  &.primary {
    background: @accent-gradient;
    color: white;
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 30px rgba(255, 107, 107, 0.4);
    }
  }

  &.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  }

  &.outline {
    background: transparent;
    color: @primary-color;
    border: 1px solid @border-color;

    &:hover {
      background: @bg-hover;
      border-color: @primary-color;
    }
  }
}

// 通用卡片样式
.card {
  background: @bg-white;
  border-radius: @radius-lg;
  box-shadow: @shadow-sm;
  border: 1px solid @border-color;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: @shadow-md;
    border-color: @primary-color;
  }
}

// 通用页面头部
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: @spacing-3xl;
  padding: @spacing-2xl @spacing-3xl;
  background: @primary-gradient;
  border-radius: @radius-xl;
  color: white;
  box-shadow: @shadow-lg;

  .header-content {
    .page-title {
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 8px 0;
      background: linear-gradient(45deg, #fff, #e2e8f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-subtitle {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
    }
  }

  .header-actions {
    display: flex;
    gap: @spacing-md;
  }
}

// 通用区域标题
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: @spacing-2xl;

  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: @text-primary;
    margin: 0;
  }

  .view-all-btn {
    display: flex;
    align-items: center;
    gap: @spacing-xs;
    background: none;
    border: none;
    color: @primary-color;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    padding: @spacing-sm @spacing-md;
    border-radius: @radius-sm;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      color: #5a67d8;
    }
  }
}

// 通用项目卡片
.project-card {
  .card();
  padding: @spacing-xl;
  cursor: pointer;
  position: relative;

  .project-thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: @primary-gradient;
    border-radius: @radius-md;
    margin-bottom: @spacing-lg;

    .project-icon {
      font-size: 28px;
    }
  }

  .project-info {
    .project-name {
      font-size: 18px;
      font-weight: 600;
      color: @text-primary;
      margin: 0 0 4px 0;
    }

    .project-path {
      font-size: 12px;
      color: @text-secondary;
      margin: 0 0 @spacing-md 0;
      font-family: 'Monaco', 'Menlo', monospace;
      word-break: break-all;
    }

    .project-meta {
      display: flex;
      gap: @spacing-md;
      flex-wrap: wrap;

      .last-modified,
      .project-type {
        font-size: 12px;
        padding: @spacing-xs @spacing-sm;
        border-radius: 6px;
        font-weight: 500;
      }

      .last-modified {
        background: rgba(34, 197, 94, 0.1);
        color: #16a34a;
      }

      .project-type {
        background: rgba(59, 130, 246, 0.1);
        color: #2563eb;
      }
    }
  }

  .project-actions {
    position: absolute;
    top: @spacing-lg;
    right: @spacing-lg;
    display: flex;
    gap: @spacing-xs;
    opacity: 0;
    transition: opacity 0.3s ease;

    .action-icon {
      width: 32px;
      height: 32px;
      border: none;
      background: rgba(255, 255, 255, 0.9);
      border-radius: @radius-sm;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      color: @text-secondary;

      &:hover {
        background: white;
        color: @primary-color;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.favorited {
        color: #fbbf24;
      }

      &.small {
        width: 24px;
        height: 24px;
      }
    }
  }

  &:hover .project-actions {
    opacity: 1;
  }
}

// 通用空状态
.empty-state {
  text-align: center;
  padding: 60px @spacing-xl;
  color: @text-secondary;

  .empty-icon {
    font-size: 64px;
    margin-bottom: @spacing-lg;
    opacity: 0.6;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #475569;
  }

  p {
    font-size: 14px;
    margin: 0 0 @spacing-2xl 0;
  }
}

// 通用动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

// 响应式断点
@mobile: ~"(max-width: 640px)";
@tablet: ~"(max-width: 768px)";
@desktop: ~"(max-width: 1024px)";

// 通用响应式样式
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: @spacing-xl;

  @media @tablet {
    grid-template-columns: 1fr;
    gap: @spacing-lg;
  }
}

.responsive-flex {
  display: flex;
  gap: @spacing-lg;

  @media @tablet {
    flex-direction: column;
    gap: @spacing-md;
  }
}