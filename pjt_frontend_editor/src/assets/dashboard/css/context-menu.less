// 右键菜单样式
@import './common.less';

.context-menu {
  position: fixed;
  background: white;
  border-radius: @radius-md;
  box-shadow: @shadow-md;
  border: 1px solid #e2e8f0;
  padding: @spacing-sm 0;
  z-index: 1000;
  min-width: 180px;

  .menu-item {
    display: flex;
    align-items: center;
    gap: @spacing-md;
    padding: @spacing-md @spacing-lg;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 14px;
    color: #374151;

    &:hover {
      background: #f3f4f6;
    }

    &.danger {
      color: #dc2626;

      &:hover {
        background: #fef2f2;
      }
    }
  }

  .menu-divider {
    height: 1px;
    background: #e5e7eb;
    margin: @spacing-xs 0;
  }
}

.context-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}