// 筛选栏样式
@import './common.less';

.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: @spacing-2xl;
  padding: @spacing-xl;
  background: @bg-white;
  border-radius: @radius-lg;
  box-shadow: @shadow-sm;
  border: 1px solid @border-color;

  .search-section {
    flex: 1;
    max-width: 400px;

    .search-input {
      position: relative;
      display: flex;
      align-items: center;

      .search-icon {
        position: absolute;
        left: @spacing-md;
        color: @text-secondary;
        font-size: 16px;
      }

      input {
        width: 100%;
        padding: @spacing-md @spacing-md @spacing-md 40px;
        border: 1px solid #e2e8f0;
        border-radius: @radius-md;
        font-size: 14px;
        background: @bg-light;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: @primary-color;
          background: white;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        &::placeholder {
          color: @text-muted;
        }
      }
    }
  }

  .filter-section {
    display: flex;
    align-items: center;
    gap: @spacing-xl;

    .filter-group {
      display: flex;
      align-items: center;
      gap: @spacing-sm;

      label {
        font-size: 14px;
        font-weight: 600;
        color: #475569;
      }

      select {
        padding: @spacing-sm @spacing-md;
        border: 1px solid #e2e8f0;
        border-radius: @radius-sm;
        font-size: 14px;
        background: white;
        cursor: pointer;

        &:focus {
          outline: none;
          border-color: @primary-color;
        }
      }
    }

    .view-toggle {
      display: flex;
      border: 1px solid #e2e8f0;
      border-radius: @radius-sm;
      overflow: hidden;

      .view-btn {
        padding: @spacing-sm @spacing-md;
        border: none;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        color: @text-secondary;

        &:hover {
          background: @bg-hover;
        }

        &.active {
          background: @primary-color;
          color: white;
        }
      }
    }
  }

  // 响应式设计
  @media @desktop {
    flex-direction: column;
    gap: @spacing-lg;
    align-items: stretch;

    .search-section {
      max-width: none;
    }

    .filter-section {
      justify-content: space-between;
    }
  }

  @media @tablet {
    .filter-section {
      flex-wrap: wrap;
      gap: @spacing-md;
    }
  }
}