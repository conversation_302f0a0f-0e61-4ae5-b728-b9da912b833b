// 列表视图样式
@import './common.less';

.projects-list {
  background: @bg-white;
  border-radius: @radius-lg;
  box-shadow: @shadow-sm;
  border: 1px solid @border-color;
  overflow: hidden;

  .list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr 1fr 120px;
    gap: @spacing-lg;
    padding: @spacing-lg @spacing-xl;
    background: @bg-light;
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
    font-weight: 600;
    color: #475569;
  }

  .list-item {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr 1fr 120px;
    gap: @spacing-lg;
    padding: @spacing-lg @spacing-xl;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.3s ease;
    align-items: center;

    &:hover {
      background: @bg-light;

      .action-icon {
        opacity: 1;
      }
    }

    &:last-child {
      border-bottom: none;
    }

    .col-name {
      display: flex;
      align-items: center;
      gap: @spacing-md;

      .project-icon-small {
        font-size: 20px;
      }

      .project-name {
        font-weight: 600;
        color: @text-primary;
      }
    }

    .col-type {
      .type-badge {
        display: inline-block;
        padding: @spacing-xs @spacing-sm;
        background: rgba(59, 130, 246, 0.1);
        color: #2563eb;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .col-path {
      font-size: 12px;
      color: @text-secondary;
      font-family: 'Monaco', 'Menlo', monospace;
      word-break: break-all;
    }

    .col-modified {
      font-size: 14px;
      color: @text-secondary;
    }

    .col-actions {
      display: flex;
      gap: @spacing-xs;
      justify-content: flex-end;

      .action-icon {
        width: 28px;
        height: 28px;
        border: none;
        background: transparent;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: @text-secondary;
        opacity: 0;

        &:hover {
          background: @bg-hover;
          color: @primary-color;
        }

        &.favorited {
          color: #fbbf24;
          opacity: 1;
        }

        &.small {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  // 响应式设计
  @media @desktop {
    .list-header,
    .list-item {
      grid-template-columns: 1fr;
      gap: @spacing-sm;
    }

    .col-path {
      display: none;
    }
  }
}