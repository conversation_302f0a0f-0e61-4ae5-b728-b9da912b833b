import {reqPost<PERSON>son} from "@/code/util/http-util.js";
import appConfig from "@/config/app-config.js";

/**
 * 后端映射
 */
export class BackendMapper {

    constructor(ctx, data = {}) {
        this.ctx = ctx;
        this.data = data;
    }

    /**
     * 请求接口
     * @param cmd
     * @param params
     * @param opts
     * @returns {Promise<*|undefined>}
     */
    async req(cmd, params, opts = {}) {
        let token = null;

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        return await reqPostJson(`${appConfig.backRootUrl}${cmd}`, {
            token,
            ...params,
        }, opts)
    }
}