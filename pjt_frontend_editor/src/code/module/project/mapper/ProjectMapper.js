import {BackendMapper} from "@/code/module/platform/mapper/BackendMapper.js";

/**
 * 项目映射
 */
export class ProjectMapper extends BackendMapper{

    constructor(ctx, data) {
        super(ctx, data);
    }

    async loadRecentList(limit = 10) {
        const r = await super.req(`/api/game-editor/project/loadRecentList`, {limit})
        if (r.success === false) {
            throw new Error(r.msg);
        }
        else {
            return r.data;
        }
    }

    async loadList() {
        const r = await super.req(`/api/game-editor/project/loadList`, {})
        if (r.success === false) {
            throw new Error(r.msg);
        }
        else {
            return r.data;
        }
    }

    async load(id) {
        const r = await super.req(`/api/game-editor/project/load`, {
            id
        })
        if (r.success === false) {
            throw new Error(r.msg);
        }
        else {
            return r.data;
        }
    }

    async save(id, name, data) {
        const r = await super.req(`/api/game-editor/project/save`, {
            id, name, data
        })
        if (r.success === false) {
            throw new Error(r.msg);
        }
        else {
            return r.data;
        }
    }

    async delete(id) {
        const r = await super.req(`/api/game-editor/project/delete`, {
            id
        })
        if (r.success === false) {
            throw new Error(r.msg);
        }
        else {
            return r.data;
        }
    }
}