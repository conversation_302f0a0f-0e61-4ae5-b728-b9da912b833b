<template>
  <div class="dashboard-home">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎回来！</h1>
        <p class="welcome-subtitle">开始创建你的下一个游戏项目</p>
        <div class="quick-actions">
          <button class="action-btn primary" @click="createNewProject">
            <el-icon class="btn-icon">
              <Plus />
            </el-icon>
            新建项目
          </button>
          <button class="action-btn secondary" @click="viewMore">
            <el-icon class="btn-icon">
              <FolderOpened />
            </el-icon>
            打开项目
          </button>
          <!-- <button class="action-btn secondary" @click="viewTemplates">
            <el-icon class="btn-icon">
              <Document />
            </el-icon>
            模板库
          </button> -->
        </div>
      </div>
      <div class="welcome-illustration">
        <div class="game-icon">🎮</div>
      </div>
    </div>

    <!-- 最近项目区域 -->
    <div class="recent-section">
      <div class="section-header">
        <h2 class="section-title">最近打开</h2>
        <button class="view-all-btn" @click="viewAllProjects">
          查看全部
          <el-icon>
            <ArrowRight />
          </el-icon>
        </button>
      </div>

      <div class="projects-grid" v-if="recentProjects.length > 0">
        <div class="project-card" v-for="item in recentProjects" :key="item.id" @click="openProject(item)">
          <div class="project-thumbnail">
            <div class="project-icon">{{ getProjectTypeIcon(item.typeId) }}</div>
          </div>
          <div class="project-info">
            <h3 class="project-name">{{ item.name }}</h3>
<!--            <p class="project-path">{{ item.path || '/path/to/project' }}</p>-->
            <div class="project-meta">
              <span class="last-modified">{{ item.lastModified || '2天前' }}</span>
              <span class="project-type">{{ getProjectTypeName(item.typeId) }}</span>
            </div>
          </div>
          <div class="project-actions">
            <button class="action-icon" @click.stop="favoriteProject(item)">
              <el-icon>
                <Star />
              </el-icon>
            </button>
            <button class="action-icon" @click.stop="moreActions(item)">
              <el-icon>
                <MoreFilled />
              </el-icon>
            </button>
          </div>
        </div>
      </div>

      <div class="empty-state" v-else>
        <div class="empty-icon">📁</div>
        <h3>还没有最近项目</h3>
        <p>创建你的第一个项目开始游戏开发之旅</p>
        <button class="action-btn primary" @click="createNewProject">
          <el-icon>
            <Plus />
          </el-icon>
          创建项目
        </button>
      </div>
    </div>

    <!-- 快速入门区域 -->
    <div class="quickstart-section">
      <h2 class="section-title">快速入门</h2>
      <div class="quickstart-grid">
        <div class="quickstart-card" @click="openTutorial('basics')">
          <div class="card-icon">📚</div>
          <h3>基础教程</h3>
          <p>学习引擎基本概念和操作</p>
        </div>
        <div class="quickstart-card" @click="openTutorial('examples')">
          <div class="card-icon">🎨</div>
          <h3>示例项目</h3>
          <p>浏览精选的游戏示例</p>
        </div>
        <div class="quickstart-card" @click="openTutorial('community')">
          <div class="card-icon">👥</div>
          <h3>社区资源</h3>
          <p>获取帮助和分享经验</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ProjectMapper} from "@/code/module/project/mapper/ProjectMapper.js";
import {toDateTimeStr} from "@/code/util/time-util.js";

/**
 * Dashboard的首页
 */
export default {
  name: 'dashboard-home',
  data() {
    return {
      // 项目类型配置
      projectTypes: {
        '2d-game': { name: '2D游戏', icon: '🎮' },
        '3d-game': { name: '3D游戏', icon: '🎯' }
      },
      // 最近打开的项目
      recentProjects: [
        // {
        //   id: '1',
        //   name: '太空射击游戏',
        //   path: '/Users/<USER>/projects/space-shooter',
        //   lastModified: '2小时前',
        //   typeId: '2d-game'
        // },
        // {
        //   id: '2',
        //   name: '平台跳跃游戏',
        //   path: '/Users/<USER>/projects/platformer',
        //   lastModified: '1天前',
        //   typeId: '2d-game'
        // },
        // {
        //   id: '3',
        //   name: '3D冒险世界',
        //   path: '/Users/<USER>/projects/adventure-3d',
        //   lastModified: '3天前',
        //   typeId: '3d-game'
        // },
        // {
        //   id: '4',
        //   name: '第一人称射击',
        //   path: '/Users/<USER>/projects/fps-game',
        //   lastModified: '5天前',
        //   typeId: '3d-game'
        // }
      ]
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    // 获取项目类型图标
    getProjectTypeIcon(typeId) {
      return this.projectTypes[typeId]?.icon || '🎮';
    },
    // 获取项目类型名称
    getProjectTypeName(typeId) {
      return this.projectTypes[typeId]?.name || '未知类型';
    },
    createNewProject() {
      console.log('创建新项目');
      // 这里可以打开新建项目对话框
    },
    viewMore() {
      console.log('打开项目');
      // 这里可以打开文件选择器
    },
    viewTemplates() {
      console.log('查看模板');
      // 这里可以打开模板库
    },
    viewAllProjects() {
      console.log('查看所有项目');
      // 切换到项目页面
    },
    openProject(project) {
      console.log('打开项目:', project.name);
      // 这里可以打开指定项目
      window.open(`${location.origin}/#/scene-editor/${project.id}`, '_blank');
    },
    favoriteProject(project) {
      console.log('收藏项目:', project.name);
      // 这里可以添加到收藏
    },
    moreActions(project) {
      console.log('更多操作:', project.name);
      // 这里可以显示更多操作菜单
    },
    openTutorial(type) {
      console.log('打开教程:', type);
      // 这里可以打开相应的教程或资源
    },
    async loadData() {
      const ctx = this.getCtx();
      this.recentProjects = (await ctx.projectMapper.loadRecentList(30)).map((n) => {
        return {
          id: n.id,
          name: n.name,
          lastModified: toDateTimeStr(new Date(n.modifiedTime)),
          typeId: '3d-game'
        }
      })
    }
  },
  async mounted() {
    const ctx = this.getCtx();
    ctx.projectMapper = new ProjectMapper(ctx);
    await this.loadData();
  }
}
</script>

<style scoped lang="less">
@import '@/assets/dashboard/css/common.less';

.dashboard-home {
  padding: @spacing-3xl;
  background: @bg-gradient;
  flex: 1;
  overflow-y: auto;

  // 欢迎区域
  .welcome-section {
    .page-header();
    
    .welcome-content {
      flex: 1;

      .welcome-title {
        font-size: 36px;
        font-weight: 700;
        margin: 0 0 8px 0;
        background: linear-gradient(45deg, #fff, #e2e8f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .welcome-subtitle {
        font-size: 18px;
        margin: 0 0 @spacing-2xl 0;
        opacity: 0.9;
        font-weight: 400;
      }

      .quick-actions {
        .responsive-flex();
      }
    }

    .welcome-illustration {
      .game-icon {
        font-size: 120px;
        opacity: 0.8;
        .float-animation();
      }
    }
  }

  // 最近项目区域
  .recent-section {
    margin-bottom: 48px;

    .projects-grid {
      .responsive-grid();
    }

    .project-card {
      .project-card();
    }

    .empty-state {
      .empty-state();
    }
  }

  // 快速入门区域
  .quickstart-section {
    margin-bottom: @spacing-3xl;

    .section-title {
      font-size: 24px;
      font-weight: 700;
      color: @text-primary;
      margin: 0 0 @spacing-2xl 0;
    }

    .quickstart-grid {
      .responsive-grid();
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .quickstart-card {
      .card();
      padding: @spacing-2xl;
      text-align: center;

      .card-icon {
        font-size: 48px;
        margin-bottom: @spacing-lg;
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: @text-primary;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 14px;
        color: @text-secondary;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  // 响应式设计
  @media @tablet {
    padding: @spacing-xl;

    .welcome-section {
      flex-direction: column;
      text-align: center;
      gap: @spacing-2xl;

      .welcome-content {
        .welcome-title {
          font-size: 28px;
        }

        .welcome-subtitle {
          font-size: 16px;
        }

        .quick-actions {
          justify-content: center;
        }
      }

      .welcome-illustration {
        .game-icon {
          font-size: 80px;
        }
      }
    }
  }
}
</style>