<template>
  <div class="dashboard-projects">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">我的项目</h1>
        <p class="page-subtitle">管理和组织你的游戏项目</p>
      </div>
      <div class="header-actions">
        <button class="action-btn primary" @click="createNewProject">
          <el-icon class="btn-icon">
            <Plus />
          </el-icon>
          新建项目
        </button>
        <button class="action-btn secondary" @click="importProject">
          <el-icon class="btn-icon">
            <FolderOpened />
          </el-icon>
          导入项目
        </button>
      </div>
    </div>

    <!-- 筛选和搜索栏 -->
    <div class="filter-bar">
      <div class="search-section">
        <div class="search-input">
          <el-icon class="search-icon">
            <Search />
          </el-icon>
          <input type="text" placeholder="搜索项目名称或路径..." v-model="searchQuery" @input="handleSearch" />
        </div>
      </div>
      <div class="filter-section">
        <div class="filter-group">
          <label>项目类型:</label>
          <select v-model="selectedType" @change="handleFilter">
            <option value="">全部类型</option>
            <option v-for="(type, id) in projectTypes" :key="id" :value="id">
              {{ type.icon }} {{ type.name }}
            </option>
          </select>
        </div>
        <div class="filter-group">
          <label>排序:</label>
          <select v-model="sortBy" @change="handleSort">
            <option value="lastModified">最近修改</option>
            <option value="name">项目名称</option>
            <option value="type">项目类型</option>
          </select>
        </div>
        <div class="view-toggle">
          <button class="view-btn" :class="{ active: viewMode === 'grid' }" @click="viewMode = 'grid'">
            <el-icon>
              <Grid />
            </el-icon>
          </button>
          <button class="view-btn" :class="{ active: viewMode === 'list' }" @click="viewMode = 'list'">
            <el-icon>
              <List />
            </el-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="projects-container">
      <div v-if="filteredProjects.length === 0" class="empty-state">
        <div class="empty-icon">📂</div>
        <h3>{{ searchQuery ? '未找到匹配的项目' : '还没有项目' }}</h3>
        <p>{{ searchQuery ? '尝试调整搜索条件' : '创建你的第一个游戏项目' }}</p>
        <button class="action-btn primary" @click="createNewProject" v-if="!searchQuery">
          <el-icon>
            <Plus />
          </el-icon>
          创建项目
        </button>
      </div>

      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="projects-grid">
        <div class="project-card" v-for="project in filteredProjects" :key="project.id" @click="openProject(project)">
          <div class="project-thumbnail">
            <div class="project-icon">{{ getProjectTypeIcon(project.typeId) }}</div>
            <div class="project-overlay">
              <button class="overlay-btn" @click.stop="openProject(project)">
                <el-icon>
                  <FolderOpened />
                </el-icon>
              </button>
            </div>
          </div>
          <div class="project-info">
            <h3 class="project-name">{{ project.name }}</h3>
            <p class="project-path">{{ project.path }}</p>
            <div class="project-meta">
              <span class="last-modified">{{ project.lastModified }}</span>
              <span class="project-type">{{ getProjectTypeName(project.typeId) }}</span>
            </div>
          </div>
          <div class="project-actions">
            <button class="action-icon" @click.stop="favoriteProject(project)"
              :class="{ favorited: project.favorited }">
              <el-icon>
                <Star />
              </el-icon>
            </button>
            <button class="action-icon" @click.stop="showProjectMenu(project, $event)">
              <el-icon>
                <MoreFilled />
              </el-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="projects-list">
        <div class="list-header">
          <div class="col-name">项目名称</div>
          <div class="col-type">类型</div>
          <div class="col-path">路径</div>
          <div class="col-modified">最后修改</div>
          <div class="col-actions">操作</div>
        </div>
        <div class="list-item" v-for="project in filteredProjects" :key="project.id" @click="openProject(project)">
          <div class="col-name">
            <div class="project-icon-small">{{ getProjectTypeIcon(project.typeId) }}</div>
            <span class="project-name">{{ project.name }}</span>
          </div>
          <div class="col-type">
            <span class="type-badge">{{ getProjectTypeName(project.typeId) }}</span>
          </div>
          <div class="col-path">{{ project.path }}</div>
          <div class="col-modified">{{ project.lastModified }}</div>
          <div class="col-actions">
            <button class="action-icon small" @click.stop="favoriteProject(project)"
              :class="{ favorited: project.favorited }">
              <el-icon>
                <Star />
              </el-icon>
            </button>
            <button class="action-icon small" @click.stop="showProjectMenu(project, $event)">
              <el-icon>
                <MoreFilled />
              </el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div v-if="contextMenu.visible" class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }" @click.stop>
      <div class="menu-item" @click="openProject(contextMenu.project)">
        <el-icon>
          <FolderOpened />
        </el-icon>
        打开项目
      </div>
      <div class="menu-item" @click="duplicateProject(contextMenu.project)">
        <el-icon>
          <CopyDocument />
        </el-icon>
        复制项目
      </div>
      <div class="menu-item" @click="renameProject(contextMenu.project)">
        <el-icon>
          <Edit />
        </el-icon>
        重命名
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item danger" @click="deleteProject(contextMenu.project)">
        <el-icon>
          <Delete />
        </el-icon>
        删除项目
      </div>
    </div>

    <!-- 遮罩层 -->
    <div v-if="contextMenu.visible" class="context-overlay" @click="hideContextMenu"></div>
  </div>
</template>

<script>
import {toDateTimeStr} from "@/code/util/time-util.js";
import {ProjectMapper} from "@/code/module/project/mapper/ProjectMapper.js";

/**
 * Dashboard的项目列表页
 */
export default {
  name: 'dashboard-projects',
  data() {
    return {
      // 项目类型配置
      projectTypes: {
        '2d-game': { name: '2D游戏', icon: '🎮' },
        '3d-game': { name: '3D游戏', icon: '🎯' }
      },
      // 搜索和筛选
      searchQuery: '',
      selectedType: '',
      sortBy: 'lastModified',
      viewMode: 'grid', // 'grid' 或 'list'

      // 右键菜单
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        project: null
      },

      // 所有项目
      projects: [
        {
          id: '1',
          name: '太空射击游戏',
          path: '/Users/<USER>/projects/space-shooter',
          lastModified: '2小时前',
          typeId: '2d-game',
          favorited: true,
        },
        {
          id: '2',
          name: '平台跳跃游戏',
          path: '/Users/<USER>/projects/platformer',
          lastModified: '1天前',
          typeId: '2d-game',
          favorited: false,
        },
        {
          id: '3',
          name: '3D冒险世界',
          path: '/Users/<USER>/projects/adventure-3d',
          lastModified: '3天前',
          typeId: '3d-game',
          favorited: true,
        },
        {
          id: '4',
          name: '第一人称射击',
          path: '/Users/<USER>/projects/fps-game',
          lastModified: '5天前',
          typeId: '3d-game',
          favorited: false,
        },
        {
          id: '5',
          name: '像素艺术游戏',
          path: '/Users/<USER>/projects/pixel-art-game',
          lastModified: '1周前',
          typeId: '2d-game',
          favorited: false,
        },
        {
          id: '6',
          name: '开放世界RPG',
          path: '/Users/<USER>/projects/open-world-rpg',
          lastModified: '2周前',
          typeId: '3d-game',
          favorited: true,
        }
      ],
    }
  },
  computed: {
    filteredProjects() {
      let filtered = [...this.projects];

      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(project =>
          project.name.toLowerCase().includes(query) ||
          project.path.toLowerCase().includes(query)
        );
      }

      // 类型过滤
      if (this.selectedType) {
        filtered = filtered.filter(project => project.typeId === this.selectedType);
      }

      // 排序
      filtered.sort((a, b) => {
        switch (this.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'type':
            return a.typeId.localeCompare(b.typeId);
          case 'lastModified':
          default:
            // 简单的时间排序，实际项目中应该使用真实的时间戳
            const timeOrder = {
              '2小时前': 1, '1天前': 2, '3天前': 3, '5天前': 4, '1周前': 5, '2周前': 6
            };
            return (timeOrder[a.lastModified] || 999) - (timeOrder[b.lastModified] || 999);
        }
      });

      return filtered;
    }
  },
  methods: {
    async onShowed() {
      await this.loadData();
    },
    getCtx() {
      return this.$ctx
    },
    // 获取项目类型图标
    getProjectTypeIcon(typeId) {
      return this.projectTypes[typeId]?.icon || '🎮';
    },
    // 获取项目类型名称
    getProjectTypeName(typeId) {
      return this.projectTypes[typeId]?.name || '未知类型';
    },
    // 搜索处理
    handleSearch() {
      // 搜索逻辑在computed中处理
    },
    // 筛选处理
    handleFilter() {
      // 筛选逻辑在computed中处理
    },
    // 排序处理
    handleSort() {
      // 排序逻辑在computed中处理
    },
    // 打开项目
    openProject(project) {
      console.log('打开项目:', project.name);
      // 这里可以打开指定项目
      window.open(`${location.origin}/#/scene-editor/${project.id}`, '_blank');
    },
    // 创建新项目
    createNewProject() {
      console.log('创建新项目');
      // 这里可以打开新建项目对话框
    },
    // 导入项目
    importProject() {
      console.log('导入项目');
      // 这里可以打开文件选择器
    },
    // 收藏项目
    favoriteProject(project) {
      project.favorited = !project.favorited;
      console.log('收藏状态:', project.name, project.favorited);
    },
    // 显示项目菜单
    showProjectMenu(project, event) {
      this.contextMenu = {
        visible: true,
        x: event.clientX,
        y: event.clientY,
        project: project
      };
    },
    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenu.visible = false;
    },
    // 复制项目
    duplicateProject(project) {
      console.log('复制项目:', project.name);
      this.hideContextMenu();
      // 这里可以实现项目复制逻辑
    },
    // 重命名项目
    renameProject(project) {
      console.log('重命名项目:', project.name);
      this.hideContextMenu();
      // 这里可以打开重命名对话框
    },
    // 删除项目
    deleteProject(project) {
      if (confirm(`确定要删除项目 "${project.name}" 吗？`)) {
        const index = this.projects.findIndex(p => p.id === project.id);
        if (index > -1) {
          this.projects.splice(index, 1);
        }
        console.log('删除项目:', project.name);
      }
      this.hideContextMenu();
    },
    async loadData() {
      const ctx = this.getCtx();
      this.projects = (await ctx.projectMapper.loadList()).map((n) => {
        return {
          id: n.id,
          name: n.name,
          lastModified: toDateTimeStr(new Date(n.modifiedTime)),
          typeId: '3d-game'
        }
      })
    }
  },
  async mounted() {
    const ctx = this.getCtx();
    ctx.projectMapper = new ProjectMapper(ctx);
    await this.onShowed();
  }
}
</script>

<style scoped lang="less">
@import '@/assets/dashboard/css/common.less';
@import '@/assets/dashboard/css/filter-bar.less';
@import '@/assets/dashboard/css/context-menu.less';
@import '@/assets/dashboard/css/list-view.less';

.dashboard-projects {
  padding: @spacing-3xl;
  background: @bg-gradient;
  flex: 1;
  overflow-y: auto;

  // 页面头部
  .page-header {
    .page-header();
  }

  // 筛选栏样式在 filter-bar.less 中定义

  // 项目容器
  .projects-container {
    .empty-state {
      .empty-state();
      padding: 80px @spacing-xl;

      .empty-icon {
        font-size: 80px;
        margin-bottom: @spacing-xl;
      }

      h3 {
        font-size: 24px;
        margin-bottom: @spacing-md;
      }

      p {
        font-size: 16px;
        margin-bottom: @spacing-3xl;
      }
    }

    // 网格视图
    .projects-grid {
      .responsive-grid();

      .project-card {
        .project-card();

        .project-thumbnail {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: @radius-lg;

          .project-icon {
            font-size: 36px;
          }

          .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: @radius-lg;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .overlay-btn {
              padding: @spacing-md;
              background: rgba(255, 255, 255, 0.9);
              border: none;
              border-radius: 50%;
              color: @primary-color;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: white;
                transform: scale(1.1);
              }
            }
          }
        }

        &:hover {
          .project-overlay {
            opacity: 1;
          }
        }
      }
    }

    // 列表视图样式在 list-view.less 中定义
  }

  // 右键菜单样式在 context-menu.less 中定义

  // 响应式设计
  @media @tablet {
    padding: @spacing-xl;

    .page-header {
      flex-direction: column;
      gap: @spacing-lg;
      text-align: center;

      .header-actions {
        justify-content: center;
      }
    }
  }
}
</style>