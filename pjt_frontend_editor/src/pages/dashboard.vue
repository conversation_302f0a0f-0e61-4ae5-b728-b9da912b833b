<template>
  <div class="dashboard">
    <div class="layer0-left-area">
      <div class="banner-area">
        <div class="logo-container">
          <div class="logo-icon">🔥</div>
          <div class="logo-text">
            <div class="brand-name">{{ banner }}</div>
            <div class="brand-subtitle">Game Engine Editor</div>
          </div>
        </div>
      </div>
      <div class="menu-area">
        <div class="menu-item" :class="{ active: activeViewId === 'home' }" @click="handleActiveView('home')">
          <el-icon class="menu-icon">
            <House />
          </el-icon>
          <span class="menu-text">首页</span>
        </div>
        <div class="menu-item" :class="{ active: activeViewId === 'projects' }" @click="handleActiveView('projects')">
          <el-icon class="menu-icon">
            <Postcard />
          </el-icon>
          <span class="menu-text">项目</span>
        </div>
      </div>
      <div class="login-state-area">
        <div class="user-info">
          <div class="avatar">👤</div>
          <div class="user-details">
            <div class="username">开发者</div>
            <div class="status">在线</div>
          </div>
        </div>
      </div>
    </div>
    <div class="layer0-main-area">
      <div class="main-content">
        <dashboard-home v-if="activeViewId === 'home'" ref="home"></dashboard-home>
        <dashboard-projects v-else-if="activeViewId === 'projects'" ref="projects"></dashboard-projects>
      </div>
    </div>
  </div>
</template>

<script>
import DashboardHome from "@/pages/components/dashboard/home.vue";
import DashboardProjects from "@/pages/components/dashboard/projects.vue";
import {waitGetObj} from "@/code/util/code-util.js";

export default {
  components: { DashboardProjects, DashboardHome },
  data() {
    return {
      banner: 'HotSpring Dashboard',
      activeViewId: 'home',
    }
  },
  methods: {
    async handleActiveView(id) {
      this.activeViewId = id;
      const view = await waitGetObj(this.$refs, id)
      if (view.onShowed) {
        await view.onShowed();
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/dashboard/css/common.less';

.dashboard {
  display: flex;
  flex-direction: row;
  height: 100vh;
  background: @primary-gradient;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  .layer0-left-area {
    width: 280px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);

    .banner-area {
      padding: @spacing-2xl @spacing-xl;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .logo-container {
        display: flex;
        align-items: center;
        gap: @spacing-md;

        .logo-icon {
          font-size: 32px;
          background: @accent-gradient;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .logo-text {
          .brand-name {
            font-size: 18px;
            font-weight: 700;
            color: white;
            margin-bottom: 2px;
          }

          .brand-subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
          }
        }
      }
    }

    .menu-area {
      flex: 1;
      padding: @spacing-xl 0;

      .menu-item {
        display: flex;
        align-items: center;
        gap: @spacing-md;
        padding: @spacing-md @spacing-xl;
        margin: @spacing-xs @spacing-md;
        border-radius: @radius-md;
        cursor: pointer;
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          transform: translateX(4px);
        }

        &.active {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
          color: white;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

          .menu-icon {
            color: #ffa500;
          }
        }

        .menu-icon {
          font-size: 18px;
          transition: color 0.3s ease;
        }

        .menu-text {
          font-size: 14px;
        }
      }
    }

    .login-state-area {
      padding: @spacing-xl;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .user-info {
        display: flex;
        align-items: center;
        gap: @spacing-md;
        padding: @spacing-md;
        background: rgba(255, 255, 255, 0.05);
        border-radius: @radius-md;

        .avatar {
          width: 36px;
          height: 36px;
          background: @accent-gradient;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
        }

        .user-details {
          .username {
            font-size: 14px;
            font-weight: 600;
            color: white;
            margin-bottom: 2px;
          }

          .status {
            font-size: 12px;
            color: #4ade80;
            font-weight: 500;
          }
        }
      }
    }
  }

  .layer0-main-area {
    flex: 1;
    display: flex;
    flex-direction: column;

    .main-content {
      flex: 1;
      background: rgba(255, 255, 255, 0.95);
      margin: @spacing-xl;
      border-radius: @radius-xl;
      box-shadow: @shadow-lg;
      backdrop-filter: blur(10px);
      overflow-y: auto;
      border: 1px solid rgba(255, 255, 255, 0.3);
      display: flex;
      flex-direction: column;
    }
  }

  // 响应式设计
  @media @tablet {
    .layer0-left-area {
      width: 240px;
    }
  }

  @media @mobile {
    .layer0-left-area {
      width: 200px;

      .banner-area {
        padding: @spacing-lg;

        .logo-text {
          .brand-name {
            font-size: 16px;
          }

          .brand-subtitle {
            font-size: 11px;
          }
        }
      }

      .menu-area {
        .menu-item {
          padding: 10px @spacing-lg;
          margin: 2px @spacing-sm;

          .menu-text {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>