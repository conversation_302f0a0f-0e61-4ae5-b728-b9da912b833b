import { createRouter, createWebHashHistory } from 'vue-router'

// 
const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('./pages/dashboard.vue')
  },
  {
    path: '/scene-editor/:projectId',
    name: 'SceneEditor',
    component: () => import('./pages/scene-editor.vue')
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router